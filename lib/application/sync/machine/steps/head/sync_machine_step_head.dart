import 'dart:async';

import 'package:bitacora/application/api/api_helper.dart';
import 'package:bitacora/application/sync/machine/sync_machine_step.dart';
import 'package:bitacora/domain/common/query/organization_common_repository_queries.dart';
import 'package:bitacora/domain/common/query/project_common_db_queries.dart';
import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/organization/organization.dart';
import 'package:bitacora/domain/project/project.dart';
import 'package:bitacora/infrastructure/organization/organization_api_translator.dart';
import 'package:bitacora/util/logger/logger.dart';

class SyncMachineStepHead extends SyncMachineStep {
  SyncMachineStepHead(super.params);

  @override
  Future<void> performSync() async {
    final response =
        await apiHelper.get('users/${session.user.remoteId!.value}/sync');

    logger.i(wrapSyncLog(stringifyStatusResponse(response)));

    var isActiveOrgDeleted = false;

    await db.transaction<void>((context) async {
      final userData = response.data['user'];
      await db.user.safeSave(context, apiTranslator.user.fromMap(userData));

      final orgsData = response.data['organizations'];
      final incomingOrgIds = <LocalId>[];
      for (final orgData in orgsData) {
        final organization = await (const OrganizationApiTranslator())
            .fromMapWithColorIfNew(context, orgData);
        final orgId = (await db.organization.save(context, organization))!;

        await syncOrgProducts(context, orgId, orgData['active_addons']);

        incomingOrgIds.add(orgId);
        final syncedProjectIds = (await db.query(
                SyncedProjectIdsRepositoryQuery(orgId: orgId),
                context: context))
            .map<LocalId>((p) => p.id!)
            .toList(growable: false);

        final projectsData = orgData['projects'];
        final incomingProjectIds = <LocalId>[];
        for (final projectData in projectsData) {
          final project = apiTranslator.project.fromMap(projectData).copyWith(
              organization: Organization(id: orgId),
              isSyncable: ProjectIsSyncable(true));
          final projectId = await db.project.save(context, project);
          incomingProjectIds.add(projectId!);
        }

        await _cleanUpNewlyUnsyncableProjects(
          context,
          syncedProjectIds,
          incomingProjectIds,
        );
      }

      final localOrgs = await db.query(
        const OrganizationIdsRepositoryQuery(),
        context: context,
      );
      final localOrgIds = localOrgs.map<LocalId>((e) => e.id!);
      final deletedOrgIds =
          localOrgIds.where((e) => !incomingOrgIds.contains(e));

      for (final deletedOrgId in deletedOrgIds) {
        isActiveOrgDeleted |= deletedOrgId == organization.id;
        // FIXME: clean up other org data
        await db.organization.delete(context, deletedOrgId);
      }
    });

    if (isActiveOrgDeleted) {
      throw 'Active org was deleted';
    }
  }

  Future<void> syncOrgProducts(
    RepositoryQueryContext context,
    LocalId orgId,
    List products,
  ) async {
    final existingProducts =
        await db.product.findByOrganization(context, orgId);
    for (final existingProduct in existingProducts) {
      await db.product.delete(context, existingProduct.id!);
    }

    // Save all new products
    for (final productData in products) {
      productData['organization_id'] = orgId.apiValue;
      final product = apiTranslator.product.fromMap(productData);
      await db.product.save(context, product);
    }
  }

  Future<void> _cleanUpNewlyUnsyncableProjects(
    RepositoryQueryContext context,
    List<LocalId> syncableProjectIds,
    List<LocalId> remoteProjectIds,
  ) async {
    final deletedProjectIds =
        syncableProjectIds.where((p) => !remoteProjectIds.contains(p));
    for (final deletedProjectId in deletedProjectIds) {
      final deletedProject = Project(
        id: deletedProjectId,
        remoteId: const RemoteId(null),
        isSyncable: ProjectIsSyncable(false),
        syncLastEntryUpdatedAt: const ProjectSyncLastEntryUpdatedAt(null),
        syncLastSyncTime: const ProjectSyncLastSyncTime(null),
        syncNextPageToken: const ProjectSyncNextPageToken(null),
      );

      await db.entry.deleteProjectRelationIfNoOutgoingMutation(
        context,
        deletedProject,
      );

      if (await db.project.hasEntries(context, deletedProjectId)) {
        await db.project.clearRemoteIdFromEntries(context, deletedProjectId);
        await db.project.save(context, deletedProject);
      } else {
        await db.project.delete(context, deletedProjectId);
      }
    }
  }

  @override
  String get debugName => 'head';
}
