import 'dart:async';

import 'package:bitacora/analytics/analytics_events.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/presentation/daylog/ai_floating_action_button/generator/daylog_ai_button_generator.dart';
import 'package:bitacora/presentation/daylog/audio/simple_audio_recorder_controller.dart';
import 'package:firebase_vertexai/firebase_vertexai.dart';
import 'package:flutter/cupertino.dart';
import 'package:mime/mime.dart';

class DaylogAiButtonFromAudioGenerator extends DaylogAiButtonSourceGenerator {
  final ValueNotifier<SimpleAudioRecorderController?>
      audioRecorderControllerNotifier;

  DaylogAiButtonFromAudioGenerator(
    super.contextSnapshot,
    this.audioRecorderControllerNotifier,
  );

  @override
  DaylogAiButtonSource get source => DaylogAiButtonSource.audio;

  @override
  AnalyticsEvent get event => AnalyticsEvent.aiAudioEntrySelected;

  @override
  Future<Entry> runInternal() async {
    logAnalytics();

    final file = await audioRecorderControllerNotifier.value!.startRecording();
    audioRecorderControllerNotifier.value = null;

    final mimeType = lookupMimeType(file.path)!;

    // Run file upload and location retrieval in parallel
    final results = await Future.wait([
      uploadTempFiles([file]),
      getEntryLocation(),
    ]);

    final fileUri = results[0].first;
    final location = results[1];
    final part = FileData(mimeType, fileUri);

    final generatedEntry = await generate([part]);

    unawaited(deleteTempFiles([file]));

    final attachment = await processFileToAttachment(file);
    return generatedEntry.copyWith(
      attachments: [],
      tags: [],
      location: location,
      source: generatedEntry.source!.copyWith(attachments: [attachment]),
    );
  }
}
