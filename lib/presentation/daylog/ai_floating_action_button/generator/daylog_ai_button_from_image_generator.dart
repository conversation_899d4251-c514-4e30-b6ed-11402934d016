import 'dart:async';

import 'package:bitacora/analytics/analytics_events.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/presentation/daylog/ai_floating_action_button/generator/daylog_ai_button_generator.dart';
import 'package:firebase_vertexai/firebase_vertexai.dart';
import 'package:mime/mime.dart';

class DaylogAiButtonFromImageGenerator extends DaylogAiButtonSourceGenerator {
  DaylogAiButtonFromImageGenerator(super.contextSnapshot);

  @override
  DaylogAiButtonSource get source => DaylogAiButtonSource.image;

  @override
  AnalyticsEvent get event => AnalyticsEvent.aiVideoEntrySelected;

  @override
  Future<Entry> runInternal() async {
    logAnalytics();

    final files = await pickFiles();
    final fileUris = await uploadTempFiles(files);

    final parts = await Future.wait(fileUris.map((i) async {
      final mimeType = lookupMimeType(i)!;
      return FileData(mimeType, i);
    }));

    final location = await getEntryLocation();
    final generatedEntry = await generate(parts);

    unawaited(deleteTempFiles(files));

    final attachments = await Future.wait(files.map((i) {
      return processFileToAttachment(i);
    }));
    return generatedEntry.copyWith(
      attachments: [],
      tags: [],
      location: location,
      source: generatedEntry.source!.copyWith(attachments: attachments),
    );
  }
}
