import 'dart:async';

import 'package:bitacora/analytics/analytics_events.dart';
import 'package:bitacora/analytics/analytics_logger.dart';
import 'package:bitacora/application/api/api_helper.dart';
import 'package:bitacora/application/cache/logday/active_log_day.dart';
import 'package:bitacora/application/cache/project/project_cache.dart';
import 'package:bitacora/domain/attachment/attachment.dart';
import 'package:bitacora/domain/common/ai_generator.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/common/storage_service.dart';
import 'package:bitacora/domain/common/value_object/lat_lng_value_object.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/infrastructure/api_translator.dart';
import 'package:bitacora/l10n/app_localizations_resolver.dart';
import 'package:bitacora/presentation/daylog/ai_floating_action_button/generator/daylog_ai_button_from_audio_generator.dart';
import 'package:bitacora/presentation/daylog/ai_floating_action_button/generator/daylog_ai_button_from_image_generator.dart';
import 'package:bitacora/presentation/daylog/ai_floating_action_button/generator/daylog_ai_button_from_video_generator.dart';
import 'package:bitacora/presentation/daylog/audio/simple_audio_recorder_controller.dart';
import 'package:bitacora/util/attachment/attachment_utils.dart';
import 'package:bitacora/util/context_snapshot/context_snapshot.dart';
import 'package:bitacora/util/location_utils.dart';
import 'package:bitacora/util/logger/logger.dart';
import 'package:bitacora/util/toast/flutter_toast_utils.dart';
import 'package:file/file.dart';
import 'package:file/local.dart';
import 'package:firebase_vertexai/firebase_vertexai.dart';
import 'package:flutter/cupertino.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path/path.dart' as path;
import 'package:uuid/uuid.dart';

enum DaylogAiButtonSource {
  audio(AnalyticsEvent.aiAudioEntrySelected, null),
  video(AnalyticsEvent.aiVideoEntrySelected, ImageSource.camera),
  image(AnalyticsEvent.aiImageEntrySelected, ImageSource.gallery);

  final AnalyticsEvent event;
  final ImageSource? source;

  const DaylogAiButtonSource(this.event, this.source);
}

abstract class DaylogAiButtonSourceGenerator {
  final DaylogAiButtonSourceRunnerContextSnapshot contextSnapshot;

  factory DaylogAiButtonSourceGenerator.build(
    DaylogAiButtonSourceRunnerContextSnapshot contextSnapshot,
    DaylogAiButtonSource source, [
    ValueNotifier<SimpleAudioRecorderController?>? audioRecorderController,
  ]) {
    switch (source) {
      case DaylogAiButtonSource.audio:
        return DaylogAiButtonFromAudioGenerator(
            contextSnapshot, audioRecorderController!);
      case DaylogAiButtonSource.video:
        return DaylogAiButtonFromVideoGenerator(contextSnapshot);
      case DaylogAiButtonSource.image:
        return DaylogAiButtonFromImageGenerator(contextSnapshot);
    }
  }

  DaylogAiButtonSourceGenerator(this.contextSnapshot);

  AiGenerator get aiGenerator => contextSnapshot.read<AiGenerator>();

  AnalyticsEvent get event;

  DaylogAiButtonSource get source;

  String get projectsText => contextSnapshot
      .read<ProjectCache>()
      .value!
      .where((p) => p.name!.value != '_' && p.name!.value != '?')
      .map((p) => '- id: ${p.remoteId!.apiValue}, name: ${p.name!.apiValue}')
      .join('\n');

  Future<Entry?> run() async {
    try {
      final entry = await runInternal();
      return entry;
    } catch (e, s) {
      handleError(e, s);
      return null;
    }
  }

  Future<Entry> runInternal();

  Future<List<File>> pickFiles() async {
    final fs = LocalFileSystem();
    final files = await _pickFiles();

    if (files.where((e) => e != null).isEmpty) {
      throw AppLocalizationsResolver.get().aiResourceNotSelected;
    }
    return files.map((e) => fs.file(e!.path)).toList(growable: false);
  }

  Future<List<String>> uploadTempFiles(List<File> files) async {
    final storageService = contextSnapshot.read<StorageService>();
    return Future.wait(files.map(
        (f) => storageService.uploadFile(f.readAsBytesSync(), f.basename)));
  }

  Future<void> deleteTempFiles(List<File> files) async {
    await Future.wait(files.map(
        (f) => contextSnapshot.read<StorageService>().deleteFile(f.basename)));
  }

  Future<List<XFile?>> _pickFiles() async {
    switch (source) {
      case DaylogAiButtonSource.audio:
        throw UnimplementedError();
      case DaylogAiButtonSource.video:
        return [
          await ImagePicker().pickVideo(
            source: source.source!,
          )
        ];
      case DaylogAiButtonSource.image:
        return ImagePicker().pickMultiImage();
    }
  }

  void handleError(dynamic e, StackTrace s) {
    logger.e(e);
    logger.e(s);
    FluttertoastUtils().showToast(msg: e.toString());
  }

  Future<Entry> generate(List<Part> parts) {
    return aiGenerator.entry.generate(
      aiGenerator,
      Content.multi([TextPart(projectsText), ...parts]),
      {'day': contextSnapshot.read<ActiveLogDay>().value!.apiValue},
    );
  }

  void logAnalytics() {
    unawaited(contextSnapshot.read<AnalyticsLogger>().logEvent(event));
  }

  Future<Attachment> processFileToAttachment(File file) async {
    logger.i('daylog-ai-button: processRecordingToAttachment');

    final processedRelativePath = await AttachmentUtils().processForSave(file);

    return Attachment(
      s3Key: AttachmentS3Key(const Uuid().v4()),
      name: AttachmentName(path.basename(processedRelativePath)),
      isUploaded: AttachmentIsUploaded(false),
      isDownloaded: AttachmentIsDownloaded(true),
      transferState:
          AttachmentTransferStateValueObject(AttachmentTransferState.na),
      path: AttachmentPath(processedRelativePath),
    );
  }

  Future<LatLngValueObject> getEntryLocation() async {
    final position = await LocationUtils().determinePosition();
    return LatLngValueObject(position.toLatLng());
  }
}

class DaylogAiButtonSourceRunnerContextSnapshot extends ContextSnapshot {
  DaylogAiButtonSourceRunnerContextSnapshot(super.context);

  DaylogAiButtonSourceRunnerContextSnapshot.fromContextSnapshot(super.snapshot)
      : super.fromSnapshot();

  @override
  List<ValueShot> get valueShots => [
        ValueShot.navigator(),
        ValueShot.provider<Repository>(),
        ValueShot.provider<ApiHelper>(),
        ValueShot.provider<ApiTranslator>(),
        ValueShot.provider<AiGenerator>(),
        ValueShot.provider<StorageService>(),
        ValueShot.provider<ActiveLogDay>(),
        ValueShot.provider<ProjectCache>(),
        ValueShot.provider<AnalyticsLogger>(),
      ];
}
